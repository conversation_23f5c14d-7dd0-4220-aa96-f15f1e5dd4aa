feat: Enhanced en passant validation and meaningful options display

## ChessKit-Swift Enhancements

### FEN Validation System Overhaul
- NEW: Added `FENValidationResult` struct with detailed validation information
- NEW: Added `validateFENDetailed()` methods returning comprehensive validation results
- CHANGED: Invalid en passant logic now treated as warning instead of error
- NEW: Added `isValidChessLogicExcludingEnPassant()` for modular validation
- NEW: Added `getValidEnPassantSquares()` returning only meaningful en passant options

### Meaningful En Passant Logic
- ENHANCED: En passant squares now only returned when opponent has pawns that can actually capture
- LOGIC: For white to move: only show en passant if white has adjacent pawn on rank 5
- LOGIC: For black to move: only show en passant if black has adjacent pawn on rank 4
- IMPROVED: Eliminates display of technically valid but practically meaningless en passant squares

## MacChessBase UI Improvements

### Position Editor Enhancements
- NEW: Added editable en passant target field with real-time validation
- NEW: Interactive grid of meaningful en passant options with click-to-select
- NEW: Visual feedback showing current selection with highlighted backgrounds
- NEW: Adaptive grid layout (max 5 columns) that handles multiple options gracefully
- NEW: En passant validation warning in section label: "En Passant (Invalid)"

### Enhanced FEN Validation Display
- IMPROVED: FEN validation status now shows three states:
  - Green "Valid FEN" for fully valid positions
  - Orange "Invalid en passant" for positions with en passant warnings
  - Red "Invalid FEN" for positions with structural errors
- ENHANCED: Separate validation indicators for en passant vs general FEN errors

### UI/UX Refinements
- IMPROVED: "Position Settings" title centered while other elements remain left-aligned
- ENHANCED: Monospaced font for consistent en passant square display
- OPTIMIZED: Compact button sizing (20x16px) for better space utilization
- REORDERED: "-" option now appears first in valid options, followed by meaningful squares
- RESPONSIVE: Grid layout adapts to number of available options automatically

## Technical Improvements

### Architecture
- MAINTAINED: All 175+ existing ChessKit tests pass
- MAINTAINED: Backward compatibility with existing validation API
- ADDED: New detailed validation methods alongside existing boolean methods
- ENHANCED: Modular validation system allowing granular error reporting

### Performance & Quality
- TESTED: Comprehensive test coverage for new validation logic
- OPTIMIZED: Efficient meaningful en passant calculation
- VALIDATED: Build system compatibility across ChessKit and MacChessBase
- DOCUMENTED: Clear method documentation with usage examples

## User Experience Impact

### Improved Workflow
- Users can now edit en passant fields with confidence
- Clear visual distinction between errors and warnings
- Instant access to only relevant en passant options
- Professional chess diagram generation with clean en passant handling

### Enhanced Validation
- Position validation is now more nuanced and user-friendly
- Invalid en passant no longer blocks position usage
- Users get helpful suggestions for meaningful en passant squares
- Clear feedback on position validity status